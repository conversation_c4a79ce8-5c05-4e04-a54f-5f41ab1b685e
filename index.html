<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>飞行棋游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            position: relative;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .game-container {
                width: 100vw !important;
                height: 100vh !important;
                max-width: none !important;
                max-height: none !important;
            }

            #gameCanvas {
                width: 100% !important;
                height: 100% !important;
                border-radius: 10px !important;
            }
        }

        @media (max-height: 600px) {
            .game-container {
                height: 100vh !important;
            }
        }

        @media (orientation: landscape) and (max-height: 500px) {
            .controls {
                display: none;
            }
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
        }
        
        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }
        
        .game-container {
            position: relative;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 25px;
            padding: 25px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.2),
                0 8px 16px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            z-index: 10;
            transition: all 0.3s ease;
        }
        
        .game-container:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 12px 20px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }
        
        #gameCanvas {
            display: block;
            border-radius: 20px;
            box-shadow: 
                0 10px 30px rgba(0, 0, 0, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border: 3px solid rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        
        #gameCanvas:hover {
            box-shadow: 
                0 15px 40px rgba(0, 0, 0, 0.35),
                0 6px 12px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 18px;
            max-width: 80%;
            display: none;
        }
        
        .controls {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            box-shadow: 
                0 4px 15px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .control-btn:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.2));
            transform: translateY(-3px);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.6);
        }
        
        .control-btn:hover::before {
            left: 100%;
        }
        
        .control-btn:active {
            transform: translateY(-1px);
            box-shadow: 
                0 4px 15px rgba(0, 0, 0, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .game-container {
                padding: 10px;
                margin: 10px;
            }
            
            #gameCanvas {
                max-width: 100%;
                height: auto;
            }
            
            .controls {
                position: relative;
                bottom: auto;
                margin-top: 20px;
                transform: none;
                left: auto;
                justify-content: center;
                flex-wrap: wrap;
            }
        }
        
        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 600px) {
            body {
                align-items: flex-start;
                padding-top: 20px;
            }
            
            .game-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- 加载提示 -->
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>游戏加载中...</div>
        </div>
        
        <!-- 错误提示 -->
        <div class="error-message" id="errorMessage">
            <div id="errorText">游戏加载失败</div>
            <button class="control-btn" onclick="location.reload()">重新加载</button>
        </div>
        
        <!-- 游戏画布 -->
        <canvas id="gameCanvas" width="800" height="600" style="display: none;"></canvas>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button class="control-btn" onclick="toggleFullscreen()">全屏</button>
            <button class="control-btn" onclick="toggleSound()">音效</button>
            <button class="control-btn" onclick="showHelp()">帮助</button>
        </div>
    </div>
    
    <!-- 游戏脚本 -->
    <script>
        // 全局变量
        window.canvas = document.getElementById('gameCanvas');
        window.SCREEN_WIDTH = 800;
        window.SCREEN_HEIGHT = 600;
        
        // 游戏全局对象
        window.GameGlobal = {
            databus: null,
            pool: null,
            currentScene: null
        };

        // 响应式设计 - 窗口大小变化处理
        function handleResize() {
            const canvas = document.getElementById('gameCanvas');
            const container = document.querySelector('.game-container');

            // 更新canvas尺寸
            const rect = container.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;

            // 更新全局尺寸变量
            window.SCREEN_WIDTH = canvas.width;
            window.SCREEN_HEIGHT = canvas.height;

            // 通知当前场景处理大小变化
            if (window.GameGlobal.currentScene && window.GameGlobal.currentScene.handleResize) {
                window.GameGlobal.currentScene.handleResize(canvas.width, canvas.height);
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
        window.addEventListener('orientationchange', () => {
            setTimeout(handleResize, 100); // 延迟处理方向变化
        });
        
        // 加载状态管理
        let isGameLoaded = false;
        let loadingElement = document.getElementById('loading');
        let errorElement = document.getElementById('errorMessage');
        let canvasElement = document.getElementById('gameCanvas');
        
        // 显示错误信息
        function showError(message) {
            console.error('游戏错误:', message);
            loadingElement.style.display = 'none';
            canvasElement.style.display = 'none';
            document.getElementById('errorText').textContent = message;
            errorElement.style.display = 'block';
        }
        
        // 游戏加载完成
        function onGameLoaded() {
            isGameLoaded = true;
            loadingElement.style.display = 'none';
            errorElement.style.display = 'none';
            canvasElement.style.display = 'block';
            console.log('游戏加载完成');
        }
        
        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }
        
        // 音效切换
        function toggleSound() {
            if (window.audioManager) {
                window.audioManager.toggleMute();
            }
        }
        
        // 显示帮助
        function showHelp() {
            alert('飞行棋游戏规则：\n\n1. 投掷骰子移动棋子\n2. 投出6点可以起飞或再投一次\n3. 连续3次6点跳过回合\n4. 棋子可以击落对手\n5. 同色棋子可以叠子\n6. 率先将4枚棋子送到终点获胜');
        }
        
        // 错误处理
        window.addEventListener('error', function(e) {
            if (!isGameLoaded) {
                showError('游戏加载失败，请检查网络连接');
            }
        });
        
        // 未捕获的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise错误:', e.reason);
            if (!isGameLoaded) {
                showError('游戏初始化失败');
            }
        });
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (window.audioManager) {
                if (document.hidden) {
                    window.audioManager.pauseAll();
                } else {
                    window.audioManager.resumeAll();
                }
            }
        });
        
        // 防止页面滚动
        document.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });
        
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(e) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 加载游戏
        async function loadGame() {
            try {
                // 动态导入游戏主文件
                const { default: Main } = await import('./js/main.js');
                
                // 创建游戏实例
                window.gameInstance = new Main();
                
                // 标记游戏加载完成
                onGameLoaded();
                
            } catch (error) {
                console.error('加载游戏失败:', error);
                showError('游戏加载失败，请刷新页面重试');
            }
        }
        
        // 页面加载完成后启动游戏
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadGame);
        } else {
            loadGame();
        }
    </script>
</body>
</html>