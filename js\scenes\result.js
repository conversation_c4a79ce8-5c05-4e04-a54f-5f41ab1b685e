import Scene from './scene';
import Button from '../ui/button';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render';
import Sprite from '../base/sprite';
import { ASSETS } from '../config/gameConfig';

/**
 * 结果场景类
 * 用于显示游戏结果
 */
export default class ResultScene extends Scene {
  // 背景图片
  background = null;
  // 游戏结果数据
  resultData = null;
  // 返回大厅回调
  onBackToHall = null;
  // 重新开始回调
  onRestart = null;
  // 按钮列表
  buttons = [];

  /**
   * 构造函数
   * @param {Object} options - 场景选项
   */
  constructor(options = {}) {
    super('result');
    
    this.onBackToHall = options.onBack || null;
    this.onRestart = options.onRestart || null;
    
    // 加载背景图片
    try {
      this.background = new Sprite(
        ASSETS.boardBackground,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
      
      // 设置背景颜色为浅蓝色
      this.background.setColor('#E6F3FF');
    } catch (e) {
      console.log('无法加载背景图片，将使用默认背景', e);
      this.background = new Sprite(
        null,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
      
      // 设置背景颜色为浅蓝色
      this.background.setColor('#E6F3FF');
    }
  }
  
  /**
   * 设置场景参数
   * @param {Object} params - 场景参数
   */
  setParams(params) {
    if (params.winner) {
      this.setResultData({
        winner: params.winner,
        gameTime: Date.now() - GameGlobal.databus.gameStartTime,
        players: GameGlobal.databus.players
      });
    }
  }
  
  /**
   * 初始化场景
   */
  init() {
    super.init();
    
    // 添加背景
    this.addElement(this.background);
    
    // 创建按钮
    this.createButtons();
  }
  
  /**
   * 创建按钮
   */
  createButtons() {
    const buttonWidth = 200;
    const buttonHeight = 60;
    const buttonSpacing = 20;
    const startY = SCREEN_HEIGHT / 2 + 100;
    
    // 重新开始按钮
    const restartButton = new Button({
      x: SCREEN_WIDTH / 2 - buttonWidth - buttonSpacing / 2,
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      text: '再来一局',
      style: {
        backgroundColor: '#3498DB',
        hoverColor: '#2980B9',
        pressedColor: '#21618C',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#85C1E9',
        shadowColor: 'rgba(52, 152, 219, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: true
      },
      onClick: () => {
        if (this.onRestart) {
          this.onRestart();
        }
      }
    });
    this.buttons.push(restartButton);
    this.addElement(restartButton);
    
    // 返回大厅按钮
    const backButton = new Button({
      x: SCREEN_WIDTH / 2 + buttonSpacing / 2,
      y: startY,
      width: buttonWidth,
      height: buttonHeight,
      text: '返回大厅',
      style: {
        backgroundColor: '#95A5A6',
        hoverColor: '#7F8C8D',
        pressedColor: '#6C7B7D',
        textColor: '#FFFFFF',
        fontSize: 20,
        fontWeight: 'bold',
        borderRadius: 20,
        borderWidth: 3,
        borderColor: '#BDC3C7',
        shadowColor: 'rgba(149, 165, 166, 0.4)',
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowOffsetY: 8,
        gradient: true,
        glowEffect: false
      },
      onClick: () => {
        if (this.onBackToHall) {
          this.onBackToHall();
        }
      }
    });
    this.buttons.push(backButton);
    this.addElement(backButton);
  }
  
  /**
   * 设置游戏结果数据
   * @param {Object} data - 游戏结果数据
   */
  setResultData(data) {
    this.resultData = data;
  }
  
  /**
   * 渲染场景
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    // 渲染场景元素
    super.render(ctx);
    
    // 渲染游戏结果
    if (this.resultData) {
      this.renderResult(ctx);
    }
  }
  
  /**
   * 渲染游戏结果
   * @param {Object} ctx - Canvas上下文
   */
  renderResult(ctx) {
    // 渲染标题
    ctx.fillStyle = '#000000';
    ctx.font = 'bold 40px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '游戏结束',
      SCREEN_WIDTH / 2,
      100
    );
    
    // 渲染获胜者信息
    if (this.resultData.winner) {
      ctx.fillStyle = this.resultData.winner.color;
      ctx.font = 'bold 30px Arial';
      ctx.fillText(
        `${this.resultData.winner.nickname} 获胜！`,
        SCREEN_WIDTH / 2,
        160
      );
    }
    
    // 渲染游戏统计
    const startY = 220;
    const lineHeight = 40;
    
    ctx.fillStyle = '#333333';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    
    // 游戏时长
    const gameTime = this.formatTime(this.resultData.gameTime);
    ctx.fillText(
      `游戏时长: ${gameTime}`,
      SCREEN_WIDTH / 2,
      startY
    );
    
    // 渲染玩家排名
    if (this.resultData.players) {
      ctx.font = '18px Arial';
      ctx.textAlign = 'left';
      
      // 排序玩家（已完成棋子数量降序）
      const sortedPlayers = [...this.resultData.players].sort((a, b) => 
        b.finishedCount - a.finishedCount
      );
      
      // 渲染排名标题
      ctx.fillStyle = '#000000';
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        '玩家排名',
        SCREEN_WIDTH / 2,
        startY + lineHeight
      );
      
      // 渲染排名表头
      const tableStartY = startY + 2 * lineHeight;
      const tableX = SCREEN_WIDTH / 2 - 150;
      
      ctx.textAlign = 'left';
      ctx.fillText('排名', tableX, tableStartY);
      ctx.fillText('玩家', tableX + 60, tableStartY);
      ctx.fillText('完成棋子', tableX + 200, tableStartY);
      
      // 渲染排名数据
      sortedPlayers.forEach((player, index) => {
        const y = tableStartY + (index + 1) * lineHeight;
        
        // 排名
        ctx.fillText(`${index + 1}`, tableX, y);
        
        // 玩家名称（使用玩家颜色）
        ctx.fillStyle = player.color;
        ctx.fillText(`${player.nickname}`, tableX + 60, y);
        
        // 完成棋子数量
        ctx.fillStyle = '#333333';
        ctx.fillText(`${player.finishedCount} / 4`, tableX + 200, y);
      });
    }
  }
  
  /**
   * 格式化时间
   * @param {number} ms - 毫秒数
   * @returns {string} 格式化后的时间
   */
  formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  }
}