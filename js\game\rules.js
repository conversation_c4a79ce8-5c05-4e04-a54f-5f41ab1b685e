import { BOARD_CONFIG, GAME_RULES, PIECE_STATUS, PLAYER_COLORS } from '../config/gameConfig';

/**
 * 游戏规则引擎
 * 负责处理游戏规则逻辑
 */
export default class GameRules {
  /**
   * 检查是否可以起飞
   * @param {number} diceValue - 骰子点数
   * @returns {boolean} 是否可以起飞
   */
  static canTakeOff(diceValue) {
    // 如果规则要求6点才能起飞，检查骰子值是否为6
    if (GAME_RULES.requireSixToTakeOff) {
      return diceValue === 6;
    }
    
    // 否则总是可以起飞
    return true;
  }
  
  /**
   * 检查是否可以继续投掷
   * @param {number} diceValue - 骰子点数
   * @param {number} consecutiveSix - 连续投掷6的次数
   * @returns {boolean} 是否可以继续投掷
   */
  static canRollAgain(diceValue, consecutiveSix) {
    // 如果骰子点数为6且规则允许重复投掷
    if (diceValue === 6 && GAME_RULES.repeatOnSix) {
      // 检查是否超过连续投掷6的最大次数
      return consecutiveSix < GAME_RULES.maxConsecutiveSix;
    }
    
    return false;
  }
  
  /**
   * 获取可移动的棋子
   * @param {Array} pieces - 玩家的棋子数组
   * @param {number} diceValue - 骰子点数
   * @returns {Array} 可移动的棋子数组
   */
  static getMovablePieces(pieces, diceValue) {
    const movablePieces = [];
    
    pieces.forEach(piece => {
      // 如果棋子在基地，检查是否可以起飞
      if (piece.status === PIECE_STATUS.BASE) {
        if (this.canTakeOff(diceValue)) {
          movablePieces.push(piece);
        }
      }
      // 如果棋子在跑道上
      else if (piece.status === PIECE_STATUS.RUNWAY) {
        const targetPosition = (piece.position + diceValue) % BOARD_CONFIG.totalCells;
        if (this.canMoveToPosition(piece, targetPosition, diceValue)) {
          movablePieces.push(piece);
        }
      }
      // 如果棋子在安全跑道
      else if (piece.status === PIECE_STATUS.SAFE) {
        const newSafePosition = piece.position + diceValue;
        if (newSafePosition <= 57) { // 57是终点位置
          movablePieces.push(piece);
        }
      }
    });
    
    return movablePieces;
  }
  
  /**
   * 检查是否可以移动到指定位置
   * @param {Object} piece - 棋子对象
   * @param {number} targetPosition - 目标位置
   * @param {number} diceValue - 骰子点数
   * @returns {boolean} 是否可以移动
   */
  static canMoveToPosition(piece, targetPosition, diceValue) {
    // 检查是否需要进入安全跑道
    const startPoint = BOARD_CONFIG.startPoints[piece.color];
    const distanceFromStart = this.calculateDistanceFromStart(piece.position, startPoint);
    
    // 如果移动后会超过一圈，需要进入安全跑道
    if (distanceFromStart + diceValue >= BOARD_CONFIG.totalCells) {
      return true; // 可以进入安全跑道
    }
    
    return true; // 普通移动
  }
  
  /**
   * 计算从起点的距离
   * @param {number} currentPosition - 当前位置
   * @param {number} startPoint - 起点位置
   * @returns {number} 距离
   */
  static calculateDistanceFromStart(currentPosition, startPoint) {
    if (currentPosition >= startPoint) {
      return currentPosition - startPoint;
    } else {
      return BOARD_CONFIG.totalCells - startPoint + currentPosition;
    }
  }
  
  /**
   * 移动棋子
   * @param {Object} piece - 棋子对象
   * @param {number} diceValue - 骰子点数
   * @param {Array} allPieces - 所有棋子数组
   * @returns {Object} 移动结果
   */
  static movePiece(piece, diceValue, allPieces) {
    const result = {
      success: false,
      newPosition: piece.position,
      newStatus: piece.status,
      hitPieces: [],
      jumped: false,
      flew: false,
      enteredSafe: false,
      finished: false
    };
    
    // 起飞逻辑
    if (piece.status === PIECE_STATUS.BASE) {
      if (this.canTakeOff(diceValue)) {
        const startPoint = BOARD_CONFIG.startPoints[piece.color];
        result.newPosition = startPoint;
        result.newStatus = PIECE_STATUS.RUNWAY;
        result.success = true;
        
        // 检查起飞位置是否有对手棋子
        const hitPieces = this.checkCollision(startPoint, piece.color, allPieces);
        result.hitPieces = hitPieces;
      }
      return result;
    }
    
    // 跑道移动逻辑
    if (piece.status === PIECE_STATUS.RUNWAY) {
      const startPoint = BOARD_CONFIG.startPoints[piece.color];
      const distanceFromStart = this.calculateDistanceFromStart(piece.position, startPoint);
      
      // 检查是否需要进入安全跑道
      if (distanceFromStart + diceValue >= BOARD_CONFIG.totalCells) {
        const safePosition = 52 + (distanceFromStart + diceValue - BOARD_CONFIG.totalCells);
        if (safePosition <= 57) {
          result.newPosition = safePosition;
          result.newStatus = PIECE_STATUS.SAFE;
          result.enteredSafe = true;
          result.success = true;
          
          if (safePosition === 57) {
            result.newStatus = PIECE_STATUS.FINISHED;
            result.finished = true;
          }
        }
      } else {
        // 普通移动
        let newPosition = (piece.position + diceValue) % BOARD_CONFIG.totalCells;
        result.newPosition = newPosition;
        result.success = true;
        
        // 检查碰撞
        const hitPieces = this.checkCollision(newPosition, piece.color, allPieces);
        result.hitPieces = hitPieces;
        
        // 检查跳跃
        const jumpResult = this.checkJump(newPosition, piece.color, allPieces);
        if (jumpResult.jumped) {
          result.newPosition = jumpResult.newPosition;
          result.jumped = true;
        }
        
        // 检查飞行
        const flyResult = this.checkFly(result.newPosition, piece.color);
        if (flyResult.flew) {
          result.newPosition = flyResult.newPosition;
          result.flew = true;
        }
      }
      
      return result;
    }
    
    // 安全跑道移动逻辑
    if (piece.status === PIECE_STATUS.SAFE) {
      const newPosition = piece.position + diceValue;
      if (newPosition <= 57) {
        result.newPosition = newPosition;
        result.success = true;
        
        if (newPosition === 57) {
          result.newStatus = PIECE_STATUS.FINISHED;
          result.finished = true;
        }
      }
    }
    
    return result;
  }
  
  /**
   * 检查碰撞
   * @param {number} position - 位置
   * @param {string} color - 棋子颜色
   * @param {Array} allPieces - 所有棋子
   * @returns {Array} 被击落的棋子
   */
  static checkCollision(position, color, allPieces) {
    const hitPieces = [];
    
    allPieces.forEach(piece => {
      if (piece.color !== color && 
          piece.position === position && 
          piece.status === PIECE_STATUS.RUNWAY &&
          !piece.isStacked) {
        hitPieces.push(piece);
      }
    });
    
    return hitPieces;
  }
  
  /**
   * 检查跳跃
   * @param {number} position - 当前位置
   * @param {string} color - 棋子颜色
   * @param {Array} allPieces - 所有棋子
   * @returns {Object} 跳跃结果
   */
  static checkJump(position, color, allPieces) {
    const result = { jumped: false, newPosition: position };
    
    // 检查是否在同色格子上
    const coloredCells = BOARD_CONFIG.coloredCells[color];
    if (!coloredCells || !coloredCells.includes(position)) {
      return result;
    }
    
    // 查找下一个同色格子
    const currentIndex = coloredCells.indexOf(position);
    if (currentIndex < coloredCells.length - 1) {
      const nextColoredCell = coloredCells[currentIndex + 1];
      
      // 检查路径上是否有己方棋子阻挡
      const pathBlocked = this.isPathBlocked(position, nextColoredCell, color, allPieces);
      
      if (!pathBlocked) {
        result.jumped = true;
        result.newPosition = nextColoredCell;
      }
    }
    
    return result;
  }
  
  /**
   * 检查飞行
   * @param {number} position - 当前位置
   * @param {string} color - 棋子颜色
   * @returns {Object} 飞行结果
   */
  static checkFly(position, color) {
    const result = { flew: false, newPosition: position };
    
    const flyingCells = BOARD_CONFIG.flyingCells[color];
    if (!flyingCells || !flyingCells.includes(position)) {
      return result;
    }
    
    // 找到对应的飞行目标
    const flyIndex = flyingCells.indexOf(position);
    if (flyIndex !== -1) {
      const targetIndex = (flyIndex + 1) % flyingCells.length;
      result.flew = true;
      result.newPosition = flyingCells[targetIndex];
    }
    
    return result;
  }
  
  /**
   * 检查路径是否被阻挡
   * @param {number} from - 起始位置
   * @param {number} to - 目标位置
   * @param {string} color - 棋子颜色
   * @param {Array} allPieces - 所有棋子
   * @returns {boolean} 是否被阻挡
   */
  static isPathBlocked(from, to, color, allPieces) {
    // 简化实现：检查目标位置是否有己方叠子
    const friendlyPieces = allPieces.filter(piece => 
      piece.color === color && 
      piece.position === to && 
      piece.status === PIECE_STATUS.RUNWAY
    );
    
    return friendlyPieces.length >= 2; // 如果有2个或以上己方棋子，认为被阻挡
  }
  
  /**
   * 检查叠子
   * @param {number} position - 位置
   * @param {string} color - 棋子颜色
   * @param {Array} allPieces - 所有棋子
   * @returns {Object} 叠子信息
   */
  static checkStack(position, color, allPieces) {
    const friendlyPieces = allPieces.filter(piece => 
      piece.color === color && 
      piece.position === position && 
      piece.status === PIECE_STATUS.RUNWAY
    );
    
    return {
      canStack: friendlyPieces.length > 0,
      stackWith: friendlyPieces[0] || null,
      stackCount: friendlyPieces.length
    };
  }
  
  /**
   * 检查游戏胜利条件
   * @param {Array} pieces - 玩家棋子数组
   * @returns {boolean} 是否获胜
   */
  static checkWin(pieces) {
    return pieces.every(piece => piece.status === PIECE_STATUS.FINISHED);
  }
  
  /**
   * 获取下一个玩家索引
   * @param {number} currentIndex - 当前玩家索引
   * @param {number} playerCount - 玩家总数
   * @param {number} diceValue - 骰子点数
   * @returns {number} 下一个玩家索引
   */
  static getNextPlayerIndex(currentIndex, playerCount, diceValue) {
    // 如果投掷到6，可以继续
    if (diceValue === 6) {
      return currentIndex;
    }
    
    return (currentIndex + 1) % playerCount;
  }
  
  /**
   * 处理棋子被击落
   * @param {Object} piece - 被击落的棋子
   */
  static handlePieceHit(piece) {
    piece.status = PIECE_STATUS.BASE;
    piece.position = -1;
    piece.isStacked = false;
    piece.stackWith = null;
  }
  
  /**
   * 处理棋子叠子
   * @param {Object} piece - 移动的棋子
   * @param {Object} targetPiece - 目标棋子
   */
  static handlePieceStack(piece, targetPiece) {
    if (GAME_RULES.allowStacking) {
      piece.isStacked = true;
      piece.stackWith = targetPiece.id;
      targetPiece.isStacked = true;
      targetPiece.stackWith = piece.id;
    }
  }
  
  /**
   * 获取棋子到终点的距离
   * @param {Object} piece - 棋子对象
   * @returns {number} 到终点的距离
   */
  static getDistanceToFinish(piece) {
    if (piece.status === PIECE_STATUS.FINISHED) {
      return 0;
    }
    
    if (piece.status === PIECE_STATUS.BASE) {
      // 基地到终点的距离 = 起点到终点的距离 + 1
      return BOARD_CONFIG.totalCells + BOARD_CONFIG.safeRunwayLength;
    }
    
    if (piece.status === PIECE_STATUS.RUNWAY) {
      // 计算从当前位置到对应颜色入口的距离
      const startPoint = BOARD_CONFIG.startPoints[piece.color];
      let distance = 0;
      
      if (piece.position < startPoint) {
        distance = startPoint - piece.position;
      } else {
        distance = BOARD_CONFIG.totalCells - piece.position + startPoint;
      }
      
      // 加上安全跑道的长度
      return distance + BOARD_CONFIG.safeRunwayLength;
    }
    
    if (piece.status === PIECE_STATUS.SAFE) {
      // 安全跑道中的棋子，直接返回剩余的安全跑道格子数
      return BOARD_CONFIG.safeRunwayLength - piece.position;
    }
    
    return Infinity;
  }
  
  /**
   * 移动棋子
   * @param {Object} piece - 棋子对象
   * @param {number} diceValue - 骰子点数
   * @returns {Object} 移动结果
   */
  static movePiece(piece, diceValue) {
    // 如果棋子已经到达终点，不能移动
    if (piece.status === PIECE_STATUS.FINISHED) {
      return {
        success: false,
        reason: 'piece_already_finished'
      };
    }
    
    // 如果棋子在基地，执行起飞
    if (piece.status === PIECE_STATUS.BASE) {
      return this.takeOff(piece);
    }
    
    // 如果棋子在跑道上
    if (piece.status === PIECE_STATUS.RUNWAY) {
      return this.moveOnRunway(piece, diceValue);
    }
    
    // 如果棋子在安全区域
    if (piece.status === PIECE_STATUS.SAFE) {
      return this.moveInSafeRunway(piece, diceValue);
    }
    
    return {
      success: false,
      reason: 'unknown_status'
    };
  }
  
  /**
   * 起飞
   * @param {Object} piece - 棋子对象
   * @returns {Object} 起飞结果
   */
  static takeOff(piece) {
    // 获取对应颜色的起点
    const startPoint = BOARD_CONFIG.startPoints[piece.color];
    
    // 更新棋子状态
    const newState = {
      status: PIECE_STATUS.RUNWAY,
      position: startPoint,
      isStacked: false,
      stackWith: null
    };
    
    return {
      success: true,
      newState,
      action: 'take_off',
      position: startPoint
    };
  }
  
  /**
   * 在跑道上移动
   * @param {Object} piece - 棋子对象
   * @param {number} steps - 移动步数
   * @returns {Object} 移动结果
   */
  static moveOnRunway(piece, steps) {
    // 计算新位置
    let newPosition = (piece.position + steps) % BOARD_CONFIG.totalCells;
    
    // 检查是否可以进入安全跑道
    const canEnterSafeRunway = this.checkEnterSafeRunway(piece, newPosition);
    
    if (canEnterSafeRunway.canEnter) {
      // 进入安全跑道
      return this.enterSafeRunway(piece, canEnterSafeRunway.remainingSteps);
    }
    
    // 检查是否可以飞行
    const canFly = this.checkFly(newPosition, piece.color);
    
    // 更新棋子状态
    const newState = {
      status: PIECE_STATUS.RUNWAY,
      position: newPosition,
      isStacked: false, // 默认不叠子，后续会检查
      stackWith: null
    };
    
    // 如果可以飞行，更新位置
    if (canFly.canFly) {
      newPosition = canFly.destination;
      newState.position = newPosition;
      
      return {
        success: true,
        newState,
        action: 'fly',
        position: newPosition,
        flyDestination: newPosition
      };
    }
    
    return {
      success: true,
      newState,
      action: 'move',
      position: newPosition
    };
  }
  
  /**
   * 在安全跑道中移动
   * @param {Object} piece - 棋子对象
   * @param {number} steps - 移动步数
   * @returns {Object} 移动结果
   */
  static moveInSafeRunway(piece, steps) {
    // 计算新位置
    const newPosition = piece.position + steps;
    
    // 如果超出安全跑道，无法移动
    if (newPosition >= BOARD_CONFIG.safeRunwayLength) {
      // 检查是否需要精确点数
      if (GAME_RULES.exactNumberToEnter) {
        return {
          success: false,
          reason: 'exact_number_required'
        };
      }
      
      // 到达终点
      return this.reachFinish(piece);
    }
    
    // 更新棋子状态
    const newState = {
      status: PIECE_STATUS.SAFE,
      position: newPosition,
      isStacked: false, // 默认不叠子，后续会检查
      stackWith: null
    };
    
    return {
      success: true,
      newState,
      action: 'move_safe',
      position: newPosition
    };
  }
  
  /**
   * 到达终点
   * @param {Object} piece - 棋子对象
   * @returns {Object} 到达终点结果
   */
  static reachFinish(piece) {
    // 更新棋子状态
    const newState = {
      status: PIECE_STATUS.FINISHED,
      position: BOARD_CONFIG.safeRunwayLength, // 终点位置
      isStacked: false,
      stackWith: null
    };
    
    return {
      success: true,
      newState,
      action: 'finish',
      position: BOARD_CONFIG.safeRunwayLength
    };
  }
  
  /**
   * 检查是否可以进入安全跑道
   * @param {Object} piece - 棋子对象
   * @param {number} newPosition - 新位置
   * @returns {Object} 检查结果
   */
  static checkEnterSafeRunway(piece, newPosition) {
    // 获取对应颜色的起点
    const startPoint = BOARD_CONFIG.startPoints[piece.color];
    
    // 如果棋子颜色与起点颜色相同，且新位置超过了起点
    // 计算是否可以进入安全跑道
    if (newPosition >= startPoint && piece.position < startPoint) {
      // 计算剩余步数
      const remainingSteps = newPosition - startPoint;
      
      return {
        canEnter: true,
        remainingSteps
      };
    }
    
    return {
      canEnter: false
    };
  }
  
  /**
   * 进入安全跑道
   * @param {Object} piece - 棋子对象
   * @param {number} remainingSteps - 剩余步数
   * @returns {Object} 进入安全跑道结果
   */
  static enterSafeRunway(piece, remainingSteps) {
    // 如果剩余步数超出安全跑道，到达终点
    if (remainingSteps >= BOARD_CONFIG.safeRunwayLength) {
      // 检查是否需要精确点数
      if (GAME_RULES.exactNumberToEnter && remainingSteps > BOARD_CONFIG.safeRunwayLength) {
        return {
          success: false,
          reason: 'exact_number_required'
        };
      }
      
      return this.reachFinish(piece);
    }
    
    // 更新棋子状态
    const newState = {
      status: PIECE_STATUS.SAFE,
      position: remainingSteps,
      isStacked: false,
      stackWith: null
    };
    
    return {
      success: true,
      newState,
      action: 'enter_safe',
      position: remainingSteps
    };
  }
  
  /**
   * 检查是否可以飞行
   * @param {number} position - 位置
   * @param {string} color - 棋子颜色
   * @returns {Object} 检查结果
   */
  static checkFly(position, color) {
    // 获取对应颜色的飞行格子
    const flyingCells = BOARD_CONFIG.flyingCells[color];
    
    // 如果当前位置是飞行格子
    if (flyingCells.includes(position)) {
      // 获取飞行目的地 (飞行格子数组中的下一个位置)
      const index = flyingCells.indexOf(position);
      const destination = index < flyingCells.length - 1 ? 
        flyingCells[index + 1] : 
        flyingCells[0];
      
      return {
        canFly: true,
        destination
      };
    }
    
    return {
      canFly: false
    };
  }
  
  /**
   * 检查碰撞
   * @param {Object} piece - 移动的棋子
   * @param {number} position - 新位置
   * @param {Array} allPieces - 所有棋子
   * @returns {Object} 碰撞结果
   */
  static checkCollision(piece, position, allPieces) {
    // 如果棋子在安全区域或已完成，不会发生碰撞
    if (piece.status === PIECE_STATUS.SAFE || piece.status === PIECE_STATUS.FINISHED) {
      return {
        hasCollision: false
      };
    }
    
    // 查找在同一位置的其他棋子
    const collidedPieces = allPieces.filter(p => 
      p.id !== piece.id && 
      p.status === PIECE_STATUS.RUNWAY && 
      p.position === position
    );
    
    // 如果没有碰撞，返回
    if (collidedPieces.length === 0) {
      return {
        hasCollision: false
      };
    }
    
    // 检查是否可以叠子
    if (GAME_RULES.allowStacking) {
      // 查找同色棋子
      const samColorPieces = collidedPieces.filter(p => p.color === piece.color);
      
      // 如果有同色棋子，可以叠子
      if (samColorPieces.length > 0) {
        return {
          hasCollision: true,
          canStack: true,
          stackWith: samColorPieces[0].id
        };
      }
    }
    
    // 碰撞到其他颜色的棋子，将其击落
    return {
      hasCollision: true,
      canStack: false,
      hitPieces: collidedPieces
    };
  }
  
  /**
   * 处理碰撞
   * @param {Object} piece - 移动的棋子
   * @param {Object} collision - 碰撞结果
   * @returns {Object} 处理结果
   */
  static handleCollision(piece, collision) {
    // 如果没有碰撞，返回原状态
    if (!collision.hasCollision) {
      return {
        piece: { ...piece },
        hitPieces: []
      };
    }
    
    // 如果可以叠子
    if (collision.canStack) {
      return {
        piece: {
          ...piece,
          isStacked: true,
          stackWith: collision.stackWith
        },
        hitPieces: []
      };
    }
    
    // 击落其他棋子
    const hitPieces = collision.hitPieces.map(hitPiece => ({
      ...hitPiece,
      status: PIECE_STATUS.BASE,
      position: -1,
      isStacked: false,
      stackWith: null
    }));
    
    return {
      piece: { ...piece },
      hitPieces
    };
  }
  
  /**
   * 检查游戏是否结束
   * @param {Object} player - 玩家对象
   * @returns {boolean} 是否结束
   */
  static checkGameOver(player) {
    // 检查玩家的所有棋子是否都到达终点
    return player.pieces.every(piece => piece.status === PIECE_STATUS.FINISHED);
  }
}