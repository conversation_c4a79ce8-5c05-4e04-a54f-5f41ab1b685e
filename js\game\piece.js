import Sprite from '../base/sprite';
import { PIECE_STATUS, ASSETS } from '../config/gameConfig';

/**
 * 棋子类
 * 继承自Sprite，实现棋子的状态和动画
 */
export default class Piece extends Sprite {
  // 棋子ID
  id = '';
  // 棋子颜色
  color = '';
  // 棋子状态
  status = PIECE_STATUS.BASE;
  // 棋子位置 (-1表示在基地，0-51表示在跑道上，52-57表示在安全跑道，58表示到达终点)
  position = -1;
  // 是否叠子
  isStacked = false;
  // 叠在一起的棋子ID
  stackWith = null;
  // 棋子索引 (0-3)
  index = 0;
  // 目标位置 (用于动画)
  targetX = 0;
  targetY = 0;
  // 动画状态
  isMoving = false;
  // 动画速度
  speed = 10;
  // 动画缓动系数
  easing = 0.2;
  // 高亮状态 (可选中)
  isHighlighted = false;
  // 选中状态
  isSelected = false;
  // 棋子大小
  pieceSize = 30;
  // 是否使用Canvas绘制
  useCanvas = false;

  /**
   * 构造函数
   * @param {string} color - 棋子颜色
   * @param {number} index - 棋子索引
   */
  constructor(color, index) {
    try {
      // 调用父类构造函数，设置图片路径和初始大小
      super(
        ASSETS.pieces[color],
        30, // 初始宽度
        30, // 初始高度
        0,  // 初始X坐标
        0   // 初始Y坐标
      );
    } catch (e) {
      // 如果图片加载失败，使用Canvas绘制
      console.log('无法加载棋子图片，将使用Canvas绘制', e);
      super(null, 30, 30, 0, 0);
      this.useCanvas = true;
      
      // 设置精灵颜色
      this.setColor(color);
    }
    
    this.color = color;
    this.index = index;
    this.id = `${color}_${index}`;
    this.status = PIECE_STATUS.BASE;
    this.position = -1;
  }
  
  /**
   * 设置棋子位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {boolean} immediate - 是否立即设置位置(不使用动画)
   */
  setPosition(x, y, immediate = false) {
    if (immediate) {
      this.x = x;
      this.y = y;
      this.targetX = x;
      this.targetY = y;
      this.isMoving = false;
    } else {
      this.targetX = x;
      this.targetY = y;
      this.isMoving = true;
    }
  }
  
  /**
   * 更新棋子状态
   * @param {Object} data - 棋子数据
   */
  updateState(data) {
    this.status = data.status;
    this.position = data.position;
    this.isStacked = data.isStacked;
    this.stackWith = data.stackWith;
  }
  
  /**
   * 设置棋子状态
   * @param {string} status - 棋子状态
   * @param {number} position - 棋子位置
   */
  setState(status, position) {
    this.status = status;
    this.position = position;
  }
  
  /**
   * 设置高亮状态
   * @param {boolean} highlighted - 是否高亮
   */
  setHighlight(highlighted) {
    this.isHighlighted = highlighted;
  }
  
  /**
   * 设置选中状态
   * @param {boolean} selected - 是否选中
   */
  setSelected(selected) {
    this.isSelected = selected;
  }
  
  /**
   * 设置叠子状态
   * @param {boolean} stacked - 是否叠子
   * @param {string} stackWith - 叠在一起的棋子ID
   */
  setStack(stacked, stackWith = null) {
    this.isStacked = stacked;
    this.stackWith = stackWith;
  }
  
  /**
   * 更新棋子状态
   */
  update() {
    // 更新动画
    if (this.isAnimating) {
      this.updateAnimation();
    }
    
    // 更新移动动画
    if (this.isMoving) {
      // 使用缓动动画移动棋子
      const dx = this.targetX - this.x;
      const dy = this.targetY - this.y;
      
      // 如果距离很小，直接设置到目标位置
      if (Math.abs(dx) < 0.5 && Math.abs(dy) < 0.5) {
        this.x = this.targetX;
        this.y = this.targetY;
        this.isMoving = false;
      } else {
        // 否则，使用缓动动画
        this.x += dx * this.easing;
        this.y += dy * this.easing;
      }
    }
    
    // 更新精灵
    if (this.sprite) {
      this.sprite.update();
    }
  }

  /**
   * 更新动画
   */
  updateAnimation() {
    if (!this.animation) return;
    
    this.animation.currentFrame++;
    
    // 计算动画进度
    const progress = this.animation.currentFrame / this.animation.totalFrames;
    
    if (progress >= 1) {
      // 动画完成
      this.completeAnimation();
    } else {
      // 更新位置
      this.updateAnimationPosition(progress);
    }
  }
  
  /**
   * 更新动画位置
   */
  updateAnimationPosition(progress) {
    if (!this.animation) return;
    
    const { startX, startY, endX, endY, type } = this.animation;
    
    switch (type) {
      case 'move':
        // 线性移动
        this.x = startX + (endX - startX) * progress;
        this.y = startY + (endY - startY) * progress;
        break;
        
      case 'jump':
        // 跳跃动画（抛物线）
        const jumpHeight = 50;
        this.x = startX + (endX - startX) * progress;
        this.y = startY + (endY - startY) * progress - Math.sin(progress * Math.PI) * jumpHeight;
        break;
        
      case 'fly':
        // 飞行动画（快速移动）
        const easeProgress = this.easeInOutQuad(progress);
        this.x = startX + (endX - startX) * easeProgress;
        this.y = startY + (endY - startY) * easeProgress;
        break;
        
      case 'takeoff':
        // 起飞动画
        const takeoffHeight = 80;
        this.x = startX + (endX - startX) * progress;
        this.y = startY + (endY - startY) * progress - Math.sin(progress * Math.PI * 0.5) * takeoffHeight;
        break;
        
      case 'hit':
        // 被击落动画（旋转坠落）
        this.x = startX + (endX - startX) * progress;
        this.y = startY + (endY - startY) * progress;
        this.rotation = progress * Math.PI * 4; // 旋转4圈
        break;
    }
  }
  
  /**
   * 缓动函数
   */
  easeInOutQuad(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }
  
  /**
   * 完成动画
   */
  completeAnimation() {
    if (!this.animation) return;
    
    // 设置最终位置
    this.x = this.animation.endX;
    this.y = this.animation.endY;
    this.rotation = 0;
    
    // 清除动画状态
    this.isAnimating = false;
    const callback = this.animation.callback;
    this.animation = null;
    
    // 执行回调
    if (callback) {
      callback();
    }
  }
  
  /**
   * 开始移动动画
   */
  startMoveAnimation(endX, endY, duration = 500, type = 'move', callback = null) {
    this.isAnimating = true;
    this.animation = {
      startX: this.x,
      startY: this.y,
      endX: endX,
      endY: endY,
      type: type,
      currentFrame: 0,
      totalFrames: Math.floor(duration / 16.67), // 60fps
      callback: callback
    };
  }
  
  /**
   * 获取棋子信息
   */
  getInfo() {
    return {
      id: this.id,
      color: this.color,
      status: this.status,
      position: this.position,
      isStacked: this.isStacked,
      index: this.index
    };
  }
  
  /**
   * 渲染棋子
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    if (!this.visible) return;
    
    ctx.save();
    
    // 应用变换
    ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
    if (this.rotation) {
      ctx.rotate(this.rotation);
    }
    
    // 绘制阴影（如果在动画中）
    if (this.isAnimating || this.isMoving) {
      ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
      ctx.shadowBlur = 10;
      ctx.shadowOffsetX = 5;
      ctx.shadowOffsetY = 5;
    }
    
    // 绘制高亮效果
    if (this.isHighlighted || this.isSelected) {
      const glowColor = this.isSelected ? '#FFD700' : '#FFFFFF';
      ctx.shadowColor = glowColor;
      ctx.shadowBlur = 15;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      
      // 绘制光环
      ctx.beginPath();
      ctx.arc(0, 0, this.width / 2 + 5, 0, 2 * Math.PI);
      ctx.fillStyle = `rgba(255, 255, 0, ${this.isSelected ? 0.4 : 0.2})`;
      ctx.fill();
    }
    
    if (this.useCanvas) {
      // 使用Canvas绘制棋子
      this.renderWithCanvas(ctx);
    } else {
      // 使用图片绘制棋子
      ctx.translate(-this.width / 2, -this.height / 2);
      super.render(ctx);
    }
    
    // 绘制叠子标记
    if (this.isStacked) {
      this.renderStackMarker(ctx);
    }
    
    ctx.restore();
  }
  
  /**
   * 使用Canvas绘制棋子
   */
  renderWithCanvas(ctx) {
    const radius = this.width / 2;
    
    // 绘制棋子阴影
    ctx.save();
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 4;
    
    // 绘制棋子主体渐变
    const gradient = ctx.createRadialGradient(-radius * 0.3, -radius * 0.3, 0, 0, 0, radius);
    const baseColor = this.getColorHex();
    const lightColor = this.lightenColor(baseColor, 0.3);
    const darkColor = this.darkenColor(baseColor, 0.2);
    
    gradient.addColorStop(0, lightColor);
    gradient.addColorStop(0.7, baseColor);
    gradient.addColorStop(1, darkColor);
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(0, 0, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.restore();
    
    // 绘制边框
    ctx.strokeStyle = '#2C3E50';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.arc(0, 0, radius, 0, Math.PI * 2);
    ctx.stroke();
    
    // 绘制内部边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(0, 0, radius - 2, 0, Math.PI * 2);
    ctx.stroke();
    
    // 绘制高光效果
    const highlightGradient = ctx.createRadialGradient(-radius * 0.4, -radius * 0.4, 0, -radius * 0.4, -radius * 0.4, radius * 0.6);
    highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
    highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    ctx.fillStyle = highlightGradient;
    ctx.beginPath();
    ctx.arc(-radius * 0.3, -radius * 0.3, radius * 0.5, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制棋子编号背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.beginPath();
    ctx.arc(0, 0, radius * 0.4, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.strokeStyle = '#2C3E50';
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // 绘制棋子编号
    ctx.fillStyle = '#2C3E50';
    ctx.font = `bold ${radius * 0.5}px 'Microsoft YaHei', Arial, sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(this.index + 1, 0, 0);
  }
  
  /**
   * 获取颜色十六进制值
   */
  getColorHex() {
    const colorMap = {
      'red': '#E74C3C',
      'yellow': '#F1C40F',
      'blue': '#3498DB',
      'green': '#27AE60'
    };
    
    return colorMap[this.color] || '#95A5A6';
  }
  
  /**
   * 使颜色变亮
   * @param {string} color - 十六进制颜色值
   * @param {number} percent - 变亮百分比 (0-1)
   * @returns {string} 变亮后的颜色
   */
  lightenColor(color, percent) {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent * 100);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }
  
  /**
   * 使颜色变暗
   * @param {string} color - 十六进制颜色值
   * @param {number} percent - 变暗百分比 (0-1)
   * @returns {string} 变暗后的颜色
   */
  darkenColor(color, percent) {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent * 100);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return '#' + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
      (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
      (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
  }
  
  /**
   * 绘制叠子标记
   */
  renderStackMarker(ctx) {
    const radius = this.width / 2;
    
    // 绘制叠子指示器
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(radius * 0.7, -radius * 0.7, radius * 0.3, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // 绘制叠子数量
    ctx.fillStyle = '#333333';
    ctx.font = `${radius * 0.3}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('2', radius * 0.7, -radius * 0.7);
  }
  
  /**
   * 使用Canvas绘制棋子
   * @param {Object} ctx - Canvas上下文
   */
  drawPiece(ctx) {
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    const radius = this.width / 2;
    
    // 绘制棋子主体
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.fillStyle = this.color;
    ctx.fill();
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.stroke();
    
    // 绘制棋子内部圆环
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.7, 0, 2 * Math.PI);
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 绘制棋子索引
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      (this.index + 1).toString(),
      centerX,
      centerY
    );
  }
  
  /**
   * 检查点击是否命中棋子
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否命中
   */
  checkClick(x, y) {
    // 计算点击点到棋子中心的距离
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    const distance = Math.sqrt(
      Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2)
    );
    
    // 如果距离小于棋子半径，则命中
    return distance <= this.width / 2;
  }
  
  /**
   * 播放起飞动画
   * @param {number} startX - 起始X坐标
   * @param {number} startY - 起始Y坐标
   * @param {number} endX - 目标X坐标
   * @param {number} endY - 目标Y坐标
   * @param {Function} callback - 动画完成回调
   */
  playTakeOffAnimation(startX, startY, endX, endY, callback) {
    // 设置起始位置
    this.x = startX;
    this.y = startY;
    
    // 设置目标位置
    this.targetX = endX;
    this.targetY = endY;
    
    // 设置更快的动画速度
    this.easing = 0.1;
    
    // 开始动画
    this.isMoving = true;
    
    // 创建动画完成检查定时器
    const checkInterval = setInterval(() => {
      if (!this.isMoving) {
        clearInterval(checkInterval);
        // 恢复正常动画速度
        this.easing = 0.2;
        // 调用回调函数
        if (callback) callback();
      }
    }, 100);
  }
  
  /**
   * 播放飞行动画
   * @param {number} startX - 起始X坐标
   * @param {number} startY - 起始Y坐标
   * @param {number} endX - 目标X坐标
   * @param {number} endY - 目标Y坐标
   * @param {Function} callback - 动画完成回调
   */
  playFlyAnimation(startX, startY, endX, endY, callback) {
    // 设置起始位置
    this.x = startX;
    this.y = startY;
    
    // 计算中间点 (飞行弧线的最高点)
    const midX = (startX + endX) / 2;
    const midY = Math.min(startY, endY) - 100; // 向上飞行
    
    // 创建动画帧
    let progress = 0;
    const animateFrame = () => {
      progress += 0.05;
      
      if (progress >= 1) {
        // 动画完成
        this.x = endX;
        this.y = endY;
        this.isMoving = false;
        
        // 调用回调函数
        if (callback) callback();
      } else {
        // 使用二次贝塞尔曲线计算当前位置
        const t = progress;
        const mt = 1 - t;
        
        this.x = mt * mt * startX + 2 * mt * t * midX + t * t * endX;
        this.y = mt * mt * startY + 2 * mt * t * midY + t * t * endY;
        
        // 继续动画
        requestAnimationFrame(animateFrame);
      }
    };
    
    // 开始动画
    this.isMoving = true;
    animateFrame();
  }
  
  /**
   * 播放被击落动画
   * @param {number} startX - 起始X坐标
   * @param {number} startY - 起始Y坐标
   * @param {number} endX - 目标X坐标
   * @param {number} endY - 目标Y坐标
   * @param {Function} callback - 动画完成回调
   */
  playHitAnimation(startX, startY, endX, endY, callback) {
    // 设置起始位置
    this.x = startX;
    this.y = startY;
    
    // 创建动画帧
    let progress = 0;
    let rotation = 0;
    const animateFrame = () => {
      progress += 0.05;
      rotation += 0.5; // 旋转速度
      
      if (progress >= 1) {
        // 动画完成
        this.x = endX;
        this.y = endY;
        this.isMoving = false;
        
        // 调用回调函数
        if (callback) callback();
      } else {
        // 线性插值计算当前位置
        this.x = startX + (endX - startX) * progress;
        this.y = startY + (endY - startY) * progress;
        
        // 继续动画
        requestAnimationFrame(animateFrame);
      }
    };
    
    // 开始动画
    this.isMoving = true;
    animateFrame();
  }
}