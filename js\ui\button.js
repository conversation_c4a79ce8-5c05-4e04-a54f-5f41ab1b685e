import Sprite from '../base/sprite';
import { ASSETS } from '../config/gameConfig';

/**
 * 按钮组件
 * 实现交互按钮功能
 */
export default class Button {
  // 按钮位置和大小
  x = 0;
  y = 0;
  width = 100;
  height = 40;
  // 按钮文本
  text = '';
  // 按钮精灵
  sprite = null;
  // 按钮状态
  isPressed = false;
  // 是否可见
  visible = true;
  // 是否启用
  enabled = true;
  // 按钮点击回调
  onClick = null;
  // 按钮样式
  style = {
    backgroundColor: '#4CAF50',
    hoverColor: '#45a049',
    pressedColor: '#3d8b40',
    textColor: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    borderRadius: 15,
    borderWidth: 3,
    borderColor: '#ffffff',
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowBlur: 10,
    shadowOffsetX: 0,
    shadowOffsetY: 5,
    pressedScale: 0.95,
    gradient: true,
    glowEffect: true
  };
  // 原始大小 (用于缩放动画)
  originalWidth = 0;
  originalHeight = 0;
  // 按钮动画状态
  animation = {
    isAnimating: false,
    scale: 1,
    targetScale: 1,
    speed: 0.1
  };

  /**
   * 构造函数
   * @param {Object} options - 按钮选项
   * @param {number} options.x - 按钮X坐标
   * @param {number} options.y - 按钮Y坐标
   * @param {number} options.width - 按钮宽度
   * @param {number} options.height - 按钮高度
   * @param {string} options.text - 按钮文本
   * @param {string} options.image - 按钮图片路径
   * @param {Function} options.onClick - 点击回调函数
   * @param {Object} options.style - 按钮样式
   */
  constructor(options = {}) {
    // 设置按钮属性
    this.x = options.x || 0;
    this.y = options.y || 0;
    this.width = options.width || 100;
    this.height = options.height || 40;
    this.text = options.text || '';
    this.onClick = options.onClick || null;
    
    // 保存原始大小
    this.originalWidth = this.width;
    this.originalHeight = this.height;
    
    // 合并样式
    if (options.style) {
      this.style = { ...this.style, ...options.style };
    }
    
    // 如果提供了图片路径，创建精灵
    if (options.image) {
      this.sprite = new Sprite(
        options.image,
        this.width,
        this.height,
        this.x,
        this.y
      );
    }
  }
  
  /**
   * 设置按钮位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
    
    if (this.sprite) {
      this.sprite.x = x;
      this.sprite.y = y;
    }
  }
  
  /**
   * 设置按钮大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
    this.originalWidth = width;
    this.originalHeight = height;
    
    if (this.sprite) {
      this.sprite.width = width;
      this.sprite.height = height;
    }
  }
  
  /**
   * 设置按钮文本
   * @param {string} text - 按钮文本
   */
  setText(text) {
    this.text = text;
  }
  
  /**
   * 设置按钮图片
   * @param {string} imagePath - 图片路径
   */
  setImage(imagePath) {
    if (!this.sprite) {
      this.sprite = new Sprite(
        imagePath,
        this.width,
        this.height,
        this.x,
        this.y
      );
    } else {
      this.sprite.img.src = imagePath;
    }
  }
  
  /**
   * 设置按钮可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.visible = visible;
    
    if (this.sprite) {
      this.sprite.visible = visible;
    }
  }
  
  /**
   * 设置按钮启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }
  
  /**
   * 按下按钮
   */
  press() {
    if (!this.enabled) return;
    
    this.isPressed = true;
    this.animation.isAnimating = true;
    this.animation.targetScale = this.style.pressedScale;
    
    // 播放按钮音效
    this.playButtonSound();
  }
  
  /**
   * 释放按钮
   */
  release() {
    if (!this.enabled) return;
    
    this.isPressed = false;
    this.animation.isAnimating = true;
    this.animation.targetScale = 1;
    
    // 触发点击回调
    if (this.onClick) {
      this.onClick();
    }
  }
  
  /**
   * 播放按钮音效
   */
  playButtonSound() {
    try {
      if (!ASSETS.audio.buttonClick) return;
      
      const audio = wx.createInnerAudioContext();
      audio.src = ASSETS.audio.buttonClick;
      audio.play();
    } catch (e) {
      console.log('播放按钮音效失败:', e);
    }
  }
  
  /**
   * 更新按钮状态
   */
  update() {
    if (this.animation.isAnimating) {
      // 计算当前缩放
      const diff = this.animation.targetScale - this.animation.scale;
      
      if (Math.abs(diff) < 0.01) {
        // 动画完成
        this.animation.scale = this.animation.targetScale;
        this.animation.isAnimating = false;
      } else {
        // 继续动画
        this.animation.scale += diff * this.animation.speed;
      }
      
      // 应用缩放
      this.width = this.originalWidth * this.animation.scale;
      this.height = this.originalHeight * this.animation.scale;
      
      // 调整位置以保持居中
      const offsetX = (this.originalWidth - this.width) / 2;
      const offsetY = (this.originalHeight - this.height) / 2;
      
      if (this.sprite) {
        this.sprite.width = this.width;
        this.sprite.height = this.height;
        this.sprite.x = this.x + offsetX;
        this.sprite.y = this.y + offsetY;
      }
    }
  }
  
  /**
   * 渲染按钮
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    if (!this.visible) return;
    
    // 保存上下文
    ctx.save();
    
    // 如果有精灵，渲染精灵
    if (this.sprite) {
      this.sprite.render(ctx);
    } else {
      // 绘制阴影
      if (this.style.shadowBlur > 0) {
        ctx.shadowColor = this.style.shadowColor;
        ctx.shadowBlur = this.style.shadowBlur;
        ctx.shadowOffsetX = this.style.shadowOffsetX;
        ctx.shadowOffsetY = this.isPressed ? this.style.shadowOffsetY / 2 : this.style.shadowOffsetY;
      }
      
      // 绘制按钮背景
      ctx.beginPath();
      this.drawRoundRect(ctx, this.x, this.y, this.width, this.height, this.style.borderRadius);
      
      // 设置填充颜色和渐变
      let fillColor;
      if (!this.enabled) {
        fillColor = '#CCCCCC';
      } else if (this.isPressed) {
        fillColor = this.style.pressedColor || this.darkenColor(this.style.backgroundColor, 20);
      } else {
        fillColor = this.style.backgroundColor;
      }
      
      if (this.style.gradient && this.enabled) {
        // 创建渐变
        const gradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.height);
        gradient.addColorStop(0, this.lightenColor(fillColor, 15));
        gradient.addColorStop(1, fillColor);
        ctx.fillStyle = gradient;
      } else {
        ctx.fillStyle = fillColor;
      }
      
      ctx.fill();
      
      // 重置阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
      
      // 绘制边框
      if (this.style.borderWidth > 0) {
        ctx.strokeStyle = this.style.borderColor;
        ctx.lineWidth = this.style.borderWidth;
        ctx.stroke();
      }
      
      // 绘制内部高光
      if (this.enabled && !this.isPressed) {
        ctx.beginPath();
        this.drawRoundRect(ctx, this.x + 2, this.y + 2, this.width - 4, this.height / 3, this.style.borderRadius - 2);
        const highlightGradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.height / 3);
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)');
        ctx.fillStyle = highlightGradient;
        ctx.fill();
      }
      
      // 绘制发光效果
      if (this.style.glowEffect && this.enabled && !this.isPressed) {
        ctx.beginPath();
        this.drawRoundRect(ctx, this.x - 2, this.y - 2, this.width + 4, this.height + 4, this.style.borderRadius + 2);
        ctx.strokeStyle = `rgba(255, 255, 255, 0.3)`;
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    }
    
    // 绘制按钮文本
    if (this.text) {
      // 文本阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;
      
      ctx.fillStyle = this.style.textColor;
      ctx.font = `${this.style.fontWeight || 'normal'} ${this.style.fontSize}px 'Microsoft YaHei', Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      const textY = this.isPressed ? this.y + this.height / 2 + 1 : this.y + this.height / 2;
      ctx.fillText(
        this.text,
        this.x + this.width / 2,
        textY
      );
      
      // 重置文本阴影
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    }
    
    // 如果禁用，绘制半透明覆盖层
    if (!this.enabled && !this.sprite) {
      ctx.fillStyle = 'rgba(200, 200, 200, 0.5)';
      ctx.beginPath();
      this.drawRoundRect(ctx, this.x, this.y, this.width, this.height, this.style.borderRadius);
      ctx.fill();
    }
    
    // 恢复上下文
    ctx.restore();
  }
  
  /**
   * 检查点击是否命中按钮
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否命中
   */
  checkClick(x, y) {
    if (!this.visible || !this.enabled) return false;
    
    return (
      x >= this.x &&
      x <= this.x + this.width &&
      y >= this.y &&
      y <= this.y + this.height
    );
  }
  
  /**
   * 使颜色变暗
   * @param {string} color - 颜色字符串 (#RRGGBB)
   * @param {number} percent - 变暗百分比
   * @returns {string} 变暗后的颜色
   */
  darkenColor(color, percent) {
    // 解析颜色
    const r = parseInt(color.substr(1, 2), 16);
    const g = parseInt(color.substr(3, 2), 16);
    const b = parseInt(color.substr(5, 2), 16);
    
    // 变暗
    const factor = 1 - percent / 100;
    const newR = Math.floor(r * factor);
    const newG = Math.floor(g * factor);
    const newB = Math.floor(b * factor);
    
    // 转换回颜色字符串
    return `#${this.componentToHex(newR)}${this.componentToHex(newG)}${this.componentToHex(newB)}`;
  }
  
  /**
   * 使颜色变亮
   * @param {string} color - 颜色字符串 (#RRGGBB)
   * @param {number} percent - 变亮百分比
   * @returns {string} 变亮后的颜色
   */
  lightenColor(color, percent) {
    // 解析颜色
    const r = parseInt(color.substr(1, 2), 16);
    const g = parseInt(color.substr(3, 2), 16);
    const b = parseInt(color.substr(5, 2), 16);
    
    // 变亮
    const factor = percent / 100;
    const newR = Math.floor(r + (255 - r) * factor);
    const newG = Math.floor(g + (255 - g) * factor);
    const newB = Math.floor(b + (255 - b) * factor);
    
    // 转换回颜色字符串
    return `#${this.componentToHex(newR)}${this.componentToHex(newG)}${this.componentToHex(newB)}`;
  }
  
  /**
   * 将颜色分量转换为十六进制字符串
   * @param {number} c - 颜色分量 (0-255)
   * @returns {string} 十六进制字符串
   */
  componentToHex(c) {
    const hex = Math.max(0, Math.min(255, c)).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 左上角X坐标
   * @param {number} y - 左上角Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundRect(ctx, x, y, width, height, radius) {
    if (width < 2 * radius) radius = width / 2;
    if (height < 2 * radius) radius = height / 2;
    
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  }
}